import { AudioPlayer, createAudioPlayer } from 'expo-audio';
import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, Easing, StyleSheet, View } from 'react-native';

interface CelebrationEffectProps {
  visible: boolean;
  onComplete?: () => void;
  duration?: number;
  particleCount?: number;
  intensity?: 'small' | 'medium' | 'large';
  type?: 'confetti' | 'sparkles' | 'stars' | 'achievement';
  position?: { x: number; y: number } | 'center';
  sound?: boolean;
}

interface Particle {
  id: number;
  x: Animated.Value;
  y: Animated.Value;
  rotation: Animated.Value;
  scale: Animated.Value;
  color: string;
  shape: 'circle' | 'square' | 'triangle' | 'star' | 'heart';
  opacity: Animated.Value;
}

const COLORS = {
  confetti: ['#FFC700', '#FF0055', '#2F9BFF', '#73DB73', '#FB6FFF'],
  sparkles: ['#FFD700', '#FFFFFF', '#FFF8DC', '#FFFACD', '#FFEC8B'],
  stars: ['#FFD700', '#FFA500', '#FF6347', '#FF69B4', '#BA55D3'],
  achievement: ['#FFD700', '#FFA500', '#FFFFFF', '#FFD700', '#FFFFFF']
};

const SHAPES = {
  confetti: ['circle', 'square', 'triangle'],
  sparkles: ['circle', 'star'],
  stars: ['star'],
  achievement: ['star', 'circle']
};

export const CelebrationEffect: React.FC<CelebrationEffectProps> = ({
  visible,
  onComplete,
  duration = 2000,
  particleCount = 30,
  intensity = 'medium',
  type = 'confetti',
  position = 'center',
  sound = true,
}) => {
  const particles = useRef<Particle[]>([]);
  const { width, height } = Dimensions.get('window');
  const playerRef = useRef<AudioPlayer | null>(null);

  useEffect(() => {
    let isMounted = true;
    const manageSound = async () => {
      if (sound && visible) {
        try {
          if (playerRef.current) {
            await playerRef.current.remove();
            playerRef.current = null;
          }
          const source = 
            type === 'achievement' ? require('../assets/sounds/achievement.mp3') :
            type === 'sparkles' ? require('../assets/sounds/sparkle.mp3') :
            require('../assets/sounds/pop.mp3');
          
          const newPlayer = createAudioPlayer(source);
          await newPlayer.play();
          
          if (isMounted) {
            playerRef.current = newPlayer;
          }
        } catch (error) {
          console.log('Error managing sound:', error);
        }
      } else if (playerRef.current) {
        await playerRef.current.remove();
        playerRef.current = null;
      }
    };

    manageSound();

    return () => {
      isMounted = false;
      if (playerRef.current) {
        playerRef.current.remove();
        playerRef.current = null;
      }
    };
  }, [visible, sound, type]);
  
  // Adjust particle count based on intensity
  const getParticleCount = () => {
    switch (intensity) {
      case 'small': return Math.round(particleCount * 0.5);
      case 'large': return Math.round(particleCount * 2);
      default: return particleCount;
    }
  };
  
  // Get starting position
  const getStartPosition = () => {
    if (position === 'center') {
      return { x: width / 2, y: height / 2 };
    }
    return position;
  };
  
  // Generate particles
  useEffect(() => {
    if (visible && particles.current.length === 0) {
      const startPos = getStartPosition();
      const actualParticleCount = getParticleCount();
      const colors = COLORS[type];
      const shapes = SHAPES[type];
      
      particles.current = Array.from({ length: actualParticleCount }).map((_, i) => ({
        id: i,
        x: new Animated.Value(startPos.x),
        y: new Animated.Value(startPos.y),
        rotation: new Animated.Value(0),
        scale: new Animated.Value(0),
        color: colors[Math.floor(Math.random() * colors.length)],
        shape: shapes[Math.floor(Math.random() * shapes.length)] as any,
        opacity: new Animated.Value(1),
      }));
      
      // Animate particles
      const animations = particles.current.map((particle, index) => {
        // Different animation based on type
        let angle, distance, particleDuration;
        
        switch (type) {
          case 'sparkles':
            // Sparkles move outward in a more controlled pattern
            angle = (index / actualParticleCount) * Math.PI * 2;
            distance = 50 + Math.random() * 100;
            particleDuration = 800 + Math.random() * 600;
            break;
            
          case 'stars':
            // Stars float upward
            angle = (Math.random() * 0.5 + 0.25) * Math.PI; // Mostly upward
            distance = 100 + Math.random() * 200;
            particleDuration = 1500 + Math.random() * 1000;
            break;
            
          case 'achievement':
            // Achievement stars burst outward and then float upward
            angle = (Math.random() * 2) * Math.PI;
            distance = 150 + Math.random() * 100;
            particleDuration = 2000 + Math.random() * 1000;
            break;
            
          case 'confetti':
          default:
            // Confetti bursts in all directions with gravity
            angle = Math.random() * Math.PI * 2;
            distance = 100 + Math.random() * 150;
            particleDuration = 1000 + Math.random() * 1000;
        }
        
        // Calculate end position
        const endX = startPos.x + Math.cos(angle) * distance;
        const endY = startPos.y + Math.sin(angle) * distance;
        
        // For achievement and stars, add upward drift
        const finalY = type === 'achievement' || type === 'stars' 
          ? endY - 100 - Math.random() * 100
          : endY + (type === 'confetti' ? 100 : 0); // Add gravity for confetti
        
        return Animated.parallel([
          // Move particle
          Animated.sequence([
            // Initial burst
            Animated.timing(particle.x, {
              toValue: endX,
              duration: particleDuration * 0.5,
              easing: Easing.out(Easing.quad),
              useNativeDriver: true,
            }),
            // Drift phase for certain types
            ...(type === 'achievement' || type === 'stars' ? [
              Animated.timing(particle.x, {
                toValue: endX + (Math.random() * 40 - 20),
                duration: particleDuration * 0.5,
                easing: Easing.inOut(Easing.quad),
                useNativeDriver: true,
              })
            ] : []),
          ]),
          
          // Y movement
          Animated.sequence([
            // Initial burst
            Animated.timing(particle.y, {
              toValue: endY,
              duration: particleDuration * 0.4,
              easing: type === 'confetti' ? Easing.out(Easing.quad) : Easing.out(Easing.back(1.5)),
              useNativeDriver: true,
            }),
            // Drift or fall
            Animated.timing(particle.y, {
              toValue: finalY,
              duration: particleDuration * 0.6,
              easing: type === 'confetti' ? Easing.in(Easing.quad) : Easing.inOut(Easing.quad),
              useNativeDriver: true,
            }),
          ]),
          
          // Rotate particle
          Animated.timing(particle.rotation, {
            toValue: Math.random() * 6 - 3, // Random rotation -3 to 3 full rotations
            duration: particleDuration,
            easing: Easing.linear,
            useNativeDriver: true,
          }),
          
          // Scale particle
          Animated.sequence([
            // Pop in
            Animated.timing(particle.scale, {
              toValue: type === 'sparkles' ? 0.3 + Math.random() * 0.3 : 0.5 + Math.random() * 0.5,
              duration: particleDuration * 0.2,
              easing: Easing.out(Easing.back(1.5)),
              useNativeDriver: true,
            }),
            // Hold
            Animated.timing(particle.scale, {
              toValue: type === 'sparkles' ? 0.2 + Math.random() * 0.2 : 0.4 + Math.random() * 0.4,
              duration: particleDuration * 0.5,
              easing: Easing.inOut(Easing.cubic),
              useNativeDriver: true,
            }),
            // Fade out
            Animated.timing(particle.scale, {
              toValue: 0,
              duration: particleDuration * 0.3,
              easing: Easing.in(Easing.cubic),
              useNativeDriver: true,
            }),
          ]),
          
          // Opacity
          Animated.sequence([
            // Hold opacity
            Animated.timing(particle.opacity, {
              toValue: type === 'sparkles' ? 0.8 : 1,
              duration: particleDuration * 0.7,
              easing: Easing.linear,
              useNativeDriver: true,
            }),
            // Fade out
            Animated.timing(particle.opacity, {
              toValue: 0,
              duration: particleDuration * 0.3,
              easing: Easing.in(Easing.cubic),
              useNativeDriver: true,
            }),
          ]),
        ]);
      });
      
      // Run all animations in parallel
      Animated.parallel(animations).start(() => {
        particles.current = [];
        if (onComplete) {
          onComplete();
        }
      });
    }
  }, [visible, width, height, particleCount, duration, onComplete, intensity, type, position]);

  // Render star shape
  const renderStar = (size: number, color: string) => {
    return (
      <View style={{
        width: 0,
        height: 0,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: size / 2,
        borderRightWidth: size / 2,
        borderBottomWidth: size,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: color,
        transform: [{ rotate: '180deg' }]
      }}>
        <View style={{
          position: 'absolute',
          top: size / 3,
          left: -size / 2,
          width: 0,
          height: 0,
          backgroundColor: 'transparent',
          borderStyle: 'solid',
          borderLeftWidth: size / 2,
          borderRightWidth: size / 2,
          borderBottomWidth: size,
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          borderBottomColor: color,
          transform: [{ rotate: '72deg' }]
        }} />
        <View style={{
          position: 'absolute',
          top: size / 3,
          left: -size / 2,
          width: 0,
          height: 0,
          backgroundColor: 'transparent',
          borderStyle: 'solid',
          borderLeftWidth: size / 2,
          borderRightWidth: size / 2,
          borderBottomWidth: size,
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          borderBottomColor: color,
          transform: [{ rotate: '144deg' }]
        }} />
        <View style={{
          position: 'absolute',
          top: size / 3,
          left: -size / 2,
          width: 0,
          height: 0,
          backgroundColor: 'transparent',
          borderStyle: 'solid',
          borderLeftWidth: size / 2,
          borderRightWidth: size / 2,
          borderBottomWidth: size,
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          borderBottomColor: color,
          transform: [{ rotate: '216deg' }]
        }} />
        <View style={{
          position: 'absolute',
          top: size / 3,
          left: -size / 2,
          width: 0,
          height: 0,
          backgroundColor: 'transparent',
          borderStyle: 'solid',
          borderLeftWidth: size / 2,
          borderRightWidth: size / 2,
          borderBottomWidth: size,
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          borderBottomColor: color,
          transform: [{ rotate: '288deg' }]
        }} />
      </View>
    );
  };

  // Render heart shape
  const renderHeart = (size: number, color: string) => {
    return (
      <View style={{
        width: size,
        height: size,
        backgroundColor: color,
        borderTopLeftRadius: size / 2,
        borderTopRightRadius: size / 2,
        transform: [
          { rotate: '45deg' },
          { translateX: size / 4 }
        ]
      }}>
        <View style={{
          position: 'absolute',
          width: size,
          height: size,
          left: -size / 2,
          backgroundColor: color,
          borderTopLeftRadius: size / 2,
          borderTopRightRadius: size / 2,
        }} />
      </View>
    );
  };

  if (!visible) return null;
  
  return (
    <View style={styles.container} pointerEvents="none">
      {particles.current.map((particle) => {
        const rotateStr = particle.rotation.interpolate({
          inputRange: [-3, 0, 3],
          outputRange: ['-1080deg', '0deg', '1080deg'],
        });
        
        // Determine particle style based on shape
        let particleStyle = {};
        let particleContent = null;
        
        switch (particle.shape) {
          case 'circle':
            particleStyle = {
              width: 10,
              height: 10,
              borderRadius: 5,
              backgroundColor: particle.color,
            };
            break;
          case 'square':
            particleStyle = {
              width: 10,
              height: 10,
              backgroundColor: particle.color,
            };
            break;
          case 'triangle':
            particleStyle = {
              width: 0,
              height: 0,
              backgroundColor: 'transparent',
              borderStyle: 'solid',
              borderLeftWidth: 5,
              borderRightWidth: 5,
              borderBottomWidth: 10,
              borderLeftColor: 'transparent',
              borderRightColor: 'transparent',
              borderBottomColor: particle.color,
            };
            break;
          case 'star':
            particleContent = renderStar(10, particle.color);
            break;
          case 'heart':
            particleContent = renderHeart(10, particle.color);
            break;
        }
        
        return (
          <Animated.View
            key={particle.id}
            style={[
              styles.particle,
              particleStyle,
              {
                transform: [
                  { translateX: particle.x },
                  { translateY: particle.y },
                  { rotate: rotateStr },
                  { scale: particle.scale },
                ],
                opacity: particle.opacity,
              },
            ]}
          >
            {particleContent}
          </Animated.View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  particle: {
    position: 'absolute',
  },
}); 