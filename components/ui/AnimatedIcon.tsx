import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef } from 'react';
import { Animated, Easing } from 'react-native';

interface AnimatedIconProps {
  name: keyof typeof Ionicons.glyphMap;
  size?: number;
  color?: string;
  focused?: boolean;
  animationType?: 'scale' | 'bounce' | 'rotate' | 'pulse' | 'shake';
  duration?: number;
  style?: any;
}

export const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  name,
  size = 24,
  color = '#000',
  focused = false,
  animationType = 'scale',
  duration = 300,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const rotateValue = useRef(new Animated.Value(0)).current;
  const shakeValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (focused) {
      switch (animationType) {
        case 'scale':
          Animated.spring(animatedValue, {
            toValue: 1,
            useNativeDriver: true,
            tension: 100,
            friction: 3,
          }).start();
          break;
        case 'bounce':
          Animated.sequence([
            Animated.timing(animatedValue, {
              toValue: 1.2,
              duration: duration / 2,
              easing: Easing.out(Easing.cubic),
              useNativeDriver: true,
            }),
            Animated.timing(animatedValue, {
              toValue: 1,
              duration: duration / 2,
              easing: Easing.out(Easing.cubic),
              useNativeDriver: true,
            }),
          ]).start();
          break;
        case 'rotate':
          Animated.timing(rotateValue, {
            toValue: 1,
            duration,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }).start();
          break;
        case 'pulse':
          Animated.loop(
            Animated.sequence([
              Animated.timing(animatedValue, {
                toValue: 1.1,
                duration: duration,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
              Animated.timing(animatedValue, {
                toValue: 1,
                duration: duration,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
            ])
          ).start();
          break;
        case 'shake':
          Animated.sequence([
            Animated.timing(shakeValue, {
              toValue: 10,
              duration: 50,
              useNativeDriver: true,
            }),
            Animated.timing(shakeValue, {
              toValue: -10,
              duration: 50,
              useNativeDriver: true,
            }),
            Animated.timing(shakeValue, {
              toValue: 10,
              duration: 50,
              useNativeDriver: true,
            }),
            Animated.timing(shakeValue, {
              toValue: 0,
              duration: 50,
              useNativeDriver: true,
            }),
          ]).start();
          break;
      }
    } else {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: duration,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();
      
      Animated.timing(rotateValue, {
        toValue: 0,
        duration: duration,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();
    }
  }, [focused, animationType, duration]);

  const getTransform = () => {
    const transforms: any[] = [];
    
    switch (animationType) {
      case 'scale':
      case 'bounce':
      case 'pulse':
        const scale = animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [1, 1.1],
        });
        transforms.push({ scale });
        break;
      case 'rotate':
        const rotate = rotateValue.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        });
        transforms.push({ rotate });
        break;
      case 'shake':
        transforms.push({ translateX: shakeValue });
        break;
    }
    
    return transforms;
  };

  return (
    <Animated.View
      style={[
        {
          transform: getTransform(),
        },
        style,
      ]}
    >
      <Ionicons
        name={name}
        size={size}
        color={color}
      />
    </Animated.View>
  );
};

// Preset icon configurations for common use cases
export const TabIcon: React.FC<{
  name: keyof typeof Ionicons.glyphMap;
  color: string;
  focused: boolean;
  size?: number;
}> = ({ name, color, focused, size = 24 }) => (
  <AnimatedIcon
    name={name}
    color={color}
    focused={focused}
    size={focused ? size + 2 : size}
    animationType="scale"
    duration={200}
    style={{
      marginBottom: -3,
    }}
  />
);

export const ActionIcon: React.FC<{
  name: keyof typeof Ionicons.glyphMap;
  color?: string;
  size?: number;
  onPress?: () => void;
}> = ({ name, color = '#007AFF', size = 24, onPress }) => (
  <AnimatedIcon
    name={name}
    color={color}
    size={size}
    animationType="bounce"
    duration={300}
  />
);

export const StatusIcon: React.FC<{
  name: keyof typeof Ionicons.glyphMap;
  color?: string;
  size?: number;
  active?: boolean;
}> = ({ name, color = '#34C759', size = 20, active = false }) => (
  <AnimatedIcon
    name={name}
    color={color}
    size={size}
    focused={active}
    animationType="pulse"
    duration={1000}
  />
);
