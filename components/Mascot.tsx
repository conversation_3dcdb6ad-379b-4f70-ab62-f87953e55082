import * as Haptics from 'expo-haptics';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Easing, Pressable, StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

export type MascotMood = 'happy' | 'excited' | 'neutral' | 'sad' | 'sleeping' | 'curious' | 'encouraging' | 'proud';
export type MascotPersonality = 'supportive' | 'playful' | 'calm' | 'energetic';

interface MascotProps {
  mood?: MascotMood;
  message?: string;
  showMessage?: boolean;
  size?: number;
  onPress?: () => void;
  personality?: MascotPersonality;
  streakCount?: number;
  idleAnimation?: boolean;
}

export const Mascot: React.FC<MascotProps> = ({
  mood = 'neutral',
  message,
  showMessage = false,
  size = 120,
  onPress,
  personality = 'supportive',
  streakCount = 0,
  idleAnimation = true,
}) => {
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const blinkAnim = useRef(new Animated.Value(1)).current;
  const breatheAnim = useRef(new Animated.Value(1)).current;
  const floatAnim = useRef(new Animated.Value(0)).current;
  const headTiltAnim = useRef(new Animated.Value(0)).current;
  
  // For random idle animations
  const [idleAnimationState, setIdleAnimationState] = useState(0);

  // Run different animations based on mascot's personality
  useEffect(() => {
    if (!idleAnimation) return;
    
    // Breathing animation - always running
    Animated.loop(
      Animated.sequence([
        Animated.timing(breatheAnim, {
          toValue: 1.05,
          duration: 2000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(breatheAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Personality-based idle animations
    let idleAnimationConfig;
    
    switch (personality) {
      case 'playful':
        // More bouncy, energetic movements
        idleAnimationConfig = {
          bounceDuration: 1200,
          bounceHeight: -8,
          floatRange: [-3, 3],
          floatDuration: 2000,
        };
        break;
      case 'calm':
        // Subtle, slow movements
        idleAnimationConfig = {
          bounceDuration: 2000,
          bounceHeight: -3,
          floatRange: [-1, 1],
          floatDuration: 3000,
        };
        break;
      case 'energetic':
        // Quick, lively movements
        idleAnimationConfig = {
          bounceDuration: 800,
          bounceHeight: -10,
          floatRange: [-5, 5],
          floatDuration: 1500,
        };
        break;
      case 'supportive':
      default:
        // Balanced, friendly movements
        idleAnimationConfig = {
          bounceDuration: 1500,
          bounceHeight: -5,
          floatRange: [-2, 2],
          floatDuration: 2500,
        };
    }
    
    // Gentle bounce animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(bounceAnim, {
          toValue: 1,
          duration: idleAnimationConfig.bounceDuration,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnim, {
          toValue: 0,
          duration: idleAnimationConfig.bounceDuration,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Subtle floating side to side
    Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: idleAnimationConfig.floatDuration,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: idleAnimationConfig.floatDuration,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Blinking animation
    const blinkLoop = () => {
      Animated.sequence([
        Animated.timing(blinkAnim, {
          toValue: 0.7,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(blinkAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Random time until next blink
        setTimeout(blinkLoop, Math.random() * 3000 + 1000);
      });
    };
    blinkLoop();
    
    // Random idle animations (head tilt, looking around)
    const randomIdleAnimation = () => {
      const nextAnimation = Math.floor(Math.random() * 4);
      
      if (nextAnimation === idleAnimationState) {
        // Don't repeat the same animation twice
        setTimeout(randomIdleAnimation, 1000);
        return;
      }
      
      setIdleAnimationState(nextAnimation);
      
      switch (nextAnimation) {
        case 1: // Head tilt
          Animated.sequence([
            Animated.timing(headTiltAnim, {
              toValue: 0.5,
              duration: 500,
              easing: Easing.out(Easing.cubic),
              useNativeDriver: true,
            }),
            Animated.delay(800),
            Animated.timing(headTiltAnim, {
              toValue: 0,
              duration: 500,
              easing: Easing.inOut(Easing.cubic),
              useNativeDriver: true,
            }),
          ]).start();
          break;
        case 2: // Look around
          Animated.sequence([
            Animated.timing(rotateAnim, {
              toValue: 0.3,
              duration: 600,
              easing: Easing.inOut(Easing.cubic),
              useNativeDriver: true,
            }),
            Animated.delay(500),
            Animated.timing(rotateAnim, {
              toValue: -0.3,
              duration: 800,
              easing: Easing.inOut(Easing.cubic),
              useNativeDriver: true,
            }),
            Animated.delay(500),
            Animated.timing(rotateAnim, {
              toValue: 0,
              duration: 600,
              easing: Easing.inOut(Easing.cubic),
              useNativeDriver: true,
            }),
          ]).start();
          break;
        default:
          // Just wait before next animation
          break;
      }
      
      // Schedule next random animation
      setTimeout(randomIdleAnimation, Math.random() * 5000 + 3000);
    };
    
    randomIdleAnimation();
    
    return () => {
      // Cleanup animations if needed
    };
  }, [personality, idleAnimation]);

  // Reaction animation when mood changes
  useEffect(() => {
    // Different reactions based on mood
    switch (mood) {
      case 'excited':
        // Big bounce and spin
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: 1.5,
            duration: 300,
            easing: Easing.out(Easing.back(2)),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 500,
            easing: Easing.out(Easing.bounce),
            useNativeDriver: true,
          }),
        ]).start();
        
        Animated.sequence([
          Animated.timing(rotateAnim, {
            toValue: 2,
            duration: 800,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: 0,
            duration: 400,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      case 'happy':
        // Gentle bounce and wiggle
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: 1.2,
            duration: 200,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 400,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        
        Animated.sequence([
          Animated.timing(rotateAnim, {
            toValue: 0.5,
            duration: 200,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: -0.5,
            duration: 200,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: 0.3,
            duration: 200,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: -0.3,
            duration: 200,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: 0,
            duration: 200,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      case 'sad':
        // Slow, subtle droop
        Animated.timing(bounceAnim, {
          toValue: -0.5,
          duration: 1000,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }).start(() => {
          bounceAnim.setValue(0);
        });
        break;
        
      case 'curious':
        // Head tilt
        Animated.sequence([
          Animated.timing(headTiltAnim, {
            toValue: 0.8,
            duration: 400,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.delay(1000),
          Animated.timing(headTiltAnim, {
            toValue: 0,
            duration: 600,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      case 'proud':
        // Puff up and stand tall
        Animated.sequence([
          Animated.timing(breatheAnim, {
            toValue: 1.15,
            duration: 500,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.delay(800),
          Animated.timing(breatheAnim, {
            toValue: 1,
            duration: 600,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: 1.2,
            duration: 500,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.delay(800),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 600,
            easing: Easing.inOut(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      case 'encouraging':
        // Gentle nod
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: 0.5,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0.3,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      default:
        // Default reaction for other moods
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 500,
          easing: Easing.elastic(1),
          useNativeDriver: true,
        }).start(() => {
          rotateAnim.setValue(0);
        });
    }
  }, [mood]);

  const handlePress = () => {
    if (onPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      // Happy wiggle animation
      Animated.sequence([
        Animated.timing(rotateAnim, {
          toValue: 0.5,
          duration: 100,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: -0.5,
          duration: 100,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: 0.25,
          duration: 100,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: -0.25,
          duration: 100,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: 0,
          duration: 100,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Scale up slightly
      Animated.sequence([
        Animated.timing(breatheAnim, {
          toValue: 1.1,
          duration: 200,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(breatheAnim, {
          toValue: 1,
          duration: 200,
          easing: Easing.in(Easing.cubic),
          useNativeDriver: true,
        }),
      ]).start();
      
      onPress();
    }
  };

  // Animation interpolations
  const translateY = bounceAnim.interpolate({
    inputRange: [-1, 0, 1, 2],
    outputRange: [5, 0, -5, -15],
  });

  const translateX = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-3, 3],
  });

  const rotate = rotateAnim.interpolate({
    inputRange: [-1, 0, 1, 2],
    outputRange: ['-20deg', '0deg', '20deg', '360deg'],
  });
  
  const headTilt = headTiltAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '25deg'],
  });

  const getFaceForMood = () => {
    switch (mood) {
      case 'happy':
        return '😊';
      case 'excited':
        return '🤩';
      case 'sad':
        return '😔';
      case 'sleeping':
        return '😴';
      case 'curious':
        return '🧐';
      case 'encouraging':
        return '😌';
      case 'proud':
        return '😁';
      default:
        return '🙂';
    }
  };
  
  // Get appropriate background color based on personality
  const getMascotColor = () => {
    switch (personality) {
      case 'supportive':
        return '#FFDAB9'; // Peach
      case 'playful':
        return '#FFD1DC'; // Light pink
      case 'calm':
        return '#B0E0E6'; // Powder blue
      case 'energetic':
        return '#FFFFE0'; // Light yellow
      default:
        return '#FFDAB9';
    }
  };

  // Special streak effects
  const renderStreakEffects = () => {
    if (streakCount >= 30) {
      return (
        <View style={styles.streakEffectContainer}>
          <ThemedText style={styles.streakEffect}>✨</ThemedText>
        </View>
      );
    }
    if (streakCount >= 7) {
      return (
        <View style={styles.streakEffectContainer}>
          <ThemedText style={styles.streakEffect}>🔥</ThemedText>
        </View>
      );
    }
    return null;
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.mascotContainer,
          {
            width: size,
            height: size,
            transform: [
              { translateY },
              { translateX },
              { rotate },
              { rotateZ: headTilt },
              { scale: breatheAnim }
            ],
          },
        ]}
      >
        <Pressable onPress={handlePress}>
          <ThemedView style={[
            styles.mascot, 
            { 
              width: size, 
              height: size,
              backgroundColor: getMascotColor(),
            }
          ]}>
            <ThemedText style={styles.face}>{getFaceForMood()}</ThemedText>
            {renderStreakEffects()}
          </ThemedView>
        </Pressable>
      </Animated.View>

      {showMessage && message && (
        <ThemedView style={styles.messageContainer}>
          <ThemedText style={styles.message}>{message}</ThemedText>
        </ThemedView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  mascotContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mascot: {
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  face: {
    fontSize: 50,
  },
  messageContainer: {
    marginTop: 10,
    padding: 10,
    borderRadius: 10,
    maxWidth: 200,
  },
  message: {
    textAlign: 'center',
  },
  streakEffectContainer: {
    position: 'absolute',
    top: -15,
    right: -5,
  },
  streakEffect: {
    fontSize: 24,
  },
}); 