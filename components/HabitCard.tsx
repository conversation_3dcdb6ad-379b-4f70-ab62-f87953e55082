import * as Haptics from 'expo-haptics';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Easing, Pressable, StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

// Helper function to determine if a color is light
const isColorLight = (color: string): boolean => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  // Calculate luminance
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
  return luminance > 160; // Adjusted threshold for better balance
};

export interface Habit {
  id: string;
  name: string;
  description: string;
  icon: string;
  frequency: 'daily' | 'weekly';
  streak: number;
  completedToday: boolean;
  completedDates: string[];
  color: string;
  goal?: number;
  progress?: number;
}

interface HabitCardProps {
  habit: Habit;
  onPress: () => void;
  onComplete: () => void;
}

export const HabitCard: React.FC<HabitCardProps> = ({
  habit,
  onPress,
  onComplete,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const [isPressed, setIsPressed] = useState(false);
  
  // Initialize progress animation on mount
  useEffect(() => {
    if (habit.completedToday && habit.progress) {
      progressAnim.setValue(habit.progress);
    } else {
      progressAnim.setValue(0);
    }
  }, []);
  
  // Update progress animation when habit changes
  useEffect(() => {
    if (habit.completedToday) {
      // Animate progress to current value
      Animated.timing(progressAnim, {
        toValue: habit.progress || 0,
        duration: 1000,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false,
      }).start();
    }
  }, [habit.completedToday, habit.progress]);
  
  // Subtle bounce animation for incomplete habits
  useEffect(() => {
    if (!habit.completedToday) {
      // Subtle attention-grabbing animation
      const bounceAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: 1,
            duration: 2000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 2000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
        ])
      );
      
      bounceAnimation.start();
      
      return () => {
        bounceAnimation.stop();
      };
    }
  }, [habit.completedToday]);
  
  // Glow effect for completed habits
  useEffect(() => {
    if (habit.completedToday) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 1500,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: false,
          }),
          Animated.timing(glowAnim, {
            toValue: 0,
            duration: 1500,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: false,
          }),
        ])
      ).start();
    }
  }, [habit.completedToday]);
  
  // Special animation for long streaks
  useEffect(() => {
    if (habit.streak >= 7) {
      // Subtle rotation for streak celebration
      const streakAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(rotateAnim, {
            toValue: 0.5,
            duration: 5000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: -0.5,
            duration: 5000,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
        ])
      );
      
      streakAnimation.start();
      
      return () => {
        streakAnimation.stop();
      };
    }
  }, [habit.streak]);

  const handleComplete = () => {
    // Play haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    // Animate card on completion
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.05,
        duration: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
    
    // Animate progress
    Animated.timing(progressAnim, {
      toValue: (habit.progress || 0) + (1 / (habit.goal || 1)),
      duration: 500,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false,
    }).start();
    
    onComplete();
  };

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });
  
  const translateY = bounceAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -2],
  });
  
  const rotate = rotateAnim.interpolate({
    inputRange: [-1, 0, 1],
    outputRange: ['-0.5deg', '0deg', '0.5deg'],
  });
  
  const shadowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.1, 0.25],
  });
  
  const shadowRadius = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [4, 8],
  });
  
  const getStreakIcon = () => {
    if (habit.streak >= 30) return '🏆';
    if (habit.streak >= 14) return '⭐';
    if (habit.streak >= 7) return '🔥';
    return '🔥';
  };
  
  const getStreakText = () => {
    if (habit.streak >= 30) return `Amazing! ${habit.streak} day streak!`;
    if (habit.streak >= 14) return `Great job! ${habit.streak} days!`;
    if (habit.streak >= 7) return `${habit.streak} day streak!`;
    return `${habit.streak} day${habit.streak !== 1 ? 's' : ''}`;
  };
  
  // Create a dynamic style for the card with animated properties
  const cardStyle = {
    ...styles.card,
    borderLeftColor: habit.color,
    shadowColor: habit.color,
    transform: [{ scale: isPressed ? 0.98 : 1 }]
  };
  
  // Create an animated container for the glow effect
  const AnimatedThemedView = Animated.createAnimatedComponent(ThemedView);

  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          transform: [
            { scale: scaleAnim },
            { translateY },
            { rotate }
          ],
        }
      ]}
    >
      <Pressable 
        onPress={onPress} 
        onPressIn={() => setIsPressed(true)}
        onPressOut={() => setIsPressed(false)}
        style={styles.cardPressable}
      >
        <AnimatedThemedView 
          style={[
            cardStyle,
            { 
              shadowOpacity: shadowOpacity as any,
              shadowRadius: shadowRadius as any,
            }
          ]}
        >
          <View style={styles.cardContent}>
            <View style={[styles.iconContainer, { backgroundColor: `${habit.color}20` }]}>
              <ThemedText style={styles.icon}>{habit.icon}</ThemedText>
            </View>
            <View style={styles.textContainer}>
              <ThemedText style={styles.title}>{habit.name}</ThemedText>
              <ThemedText style={styles.description}>{habit.description}</ThemedText>
              
              {habit.streak > 0 && (
                <View style={[styles.streakContainer, { backgroundColor: `${habit.color}15` }]}>
                  <ThemedText style={styles.streakText}>
                    {getStreakIcon()} {getStreakText()}
                  </ThemedText>
                </View>
              )}
            </View>
          </View>
          
          <View style={styles.progressContainer}>
            <Animated.View 
              style={[
                styles.progressBar, 
                { width: progressWidth, backgroundColor: habit.color }
              ]} 
            />
            {habit.goal && habit.goal > 1 && (
              <View style={styles.progressMarkers}>
                {Array.from({ length: habit.goal - 1 }).map((_, i) => (
                  <View 
                    key={i} 
                    style={[
                      styles.progressMarker,
                      { left: `${((i + 1) / habit.goal!) * 100}%` }
                    ]} 
                  />
                ))}
              </View>
            )}
          </View>
          
          {!habit.completedToday && (
            <Pressable 
              onPress={handleComplete} 
              style={({pressed}) => [
                styles.completeButton,
                { 
                  backgroundColor: habit.color, 
                  opacity: pressed ? 0.8 : 1,
                  transform: [
                    { scale: pressed ? 0.95 : 1 }
                  ]
                }
              ]}
            >
              <ThemedText style={[styles.completeButtonText, { color: isColorLight(habit.color) ? '#000' : '#fff' }]}>Complete</ThemedText>
            </Pressable>
          )}
          
          {habit.completedToday && (
            <View style={[styles.completedBadge, { backgroundColor: habit.color }]}>
              <ThemedText style={[styles.completedText, { color: isColorLight(habit.color) ? '#000' : '#fff' }]}>✓</ThemedText>
            </View>
          )}
        </AnimatedThemedView>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    width: '100%',
  },
  cardPressable: {
    width: '100%',
  },
  card: {
    borderRadius: 16,
    padding: 16,
    borderLeftWidth: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  icon: {
    fontSize: 24,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    opacity: 0.7,
  },
  streakContainer: {
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  streakText: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    height: 6,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 3,
    marginTop: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  progressMarkers: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  progressMarker: {
    position: 'absolute',
    width: 2,
    height: 6,
    backgroundColor: 'rgba(255,255,255,0.7)',
  },
  completeButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-end',
  },
  completeButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  completedBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedText: {
    color: '#fff',
    fontWeight: 'bold',
  },
}); 