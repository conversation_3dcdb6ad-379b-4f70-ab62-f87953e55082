# Mindful Companion - Habit Building App

A delightful mobile application designed with emotional design principles to help users build positive habits with an animated mascot companion that provides encouragement and celebrates achievements.

## Features

- **Animated Companion Mascot**: A friendly character with personality that reacts to your progress with different emotions, idle animations, and provides encouragement through contextual messages
- **Habit Tracking**: Create and track daily or weekly habits with customizable goals and visual progress indicators
- **Delightful Microinteractions**: Every interaction is designed to feel satisfying with smooth animations, haptic feedback, and visual rewards
- **Streak Tracking**: Build and maintain streaks with milestone celebrations and visual indicators
- **Celebration Effects**: Enjoy delightful confetti, sparkles, and achievement animations with sound effects when completing habits
- **Emotional Design**: The app adapts its mood and interactions based on time of day, progress, and user behavior

## Emotional Design Elements

- **Living Mascot**: The companion mascot features idle animations (breathing, blinking, looking around), reacts to user input, and changes personality based on user progress
- **Progress Visualization**: Progress is shown through satisfying animations and color changes to create a sense of accomplishment
- **Contextual Encouragement**: The mascot provides supportive messages based on the current context (time of day, habit completion status)
- **Celebration Hierarchy**: Different types of celebrations for different achievements (habit completion, streaks, milestones)
- **Delightful Surprises**: Small animations and interactions throughout the app to create moments of joy
- **Haptic Feedback**: Thoughtful use of haptics to create a more tangible and satisfying experience

## Technical Details

This app is built with:
- React Native / Expo
- TypeScript
- React Navigation
- Expo Router
- React Native Reanimated for smooth animations
- AsyncStorage for local data persistence
- Expo Haptics for tactile feedback

## Getting Started

1. Install dependencies:

   ```bash
   npm install
   ```

2. Start the development server:

   ```bash
   npm start
   ```

3. Follow the instructions in the terminal to open the app on your device or emulator.

## App Structure

- `app/`: Main application screens and navigation
- `components/`: Reusable UI components
  - `Mascot.tsx`: The animated mascot component with personality and mood states
  - `HabitCard.tsx`: Component for displaying habits with interactive animations
  - `CelebrationEffect.tsx`: Various celebration animations for different achievements
- `hooks/`: Custom React hooks
  - `useHabits.tsx`: State management for habits with streak tracking

## Design Philosophy

The app is built with emotional design principles in mind:
- **Character Animation**: The mascot has personality, idle animations, and reacts to completed tasks
- **Delight & Feedback**: Completing habits triggers satisfying microinteractions with visual and haptic feedback
- **Progress Animation**: Progress is visualized with smooth, pleasing animations and color changes
- **Human Feel**: Feedback is designed to feel supportive and human, with contextual messages
- **Building Trust**: Polish and attention to detail in every interaction builds trust and engagement
- **Emotional Layer**: The app creates a positive emotional experience beyond just functionality

## License

MIT
