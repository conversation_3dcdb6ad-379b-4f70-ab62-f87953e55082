import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { HabitProgressCard, StatsCard } from '@/components/ui/AnimatedCard';
import { GradientBackground } from '@/components/ui/GradientBackground';
import { useHabits } from '@/hooks/useHabits';
import React, { useEffect, useState } from 'react';
import { Animated, Easing, ScrollView, StyleSheet, View } from 'react-native';

export default function ProgressScreen() {
  const { habits } = useHabits();
  const [fadeAnim] = useState(new Animated.Value(0));

  // Animate screen entry
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start();
  }, []);

  // Calculate statistics
  const totalHabits = habits.length;
  const completedToday = habits.filter(h => h.completedToday).length;
  const completionRate = totalHabits > 0 ? Math.round((completedToday / totalHabits) * 100) : 0;

  // Find the habit with the longest streak
  const longestStreakHabit = habits.reduce((longest, current) =>
    current.streak > (longest?.streak || 0) ? current : longest, habits[0]);

  // Calculate weekly completion rate
  const last7Days = [...Array(7)].map((_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i);
    return date.toISOString().split('T')[0];
  });

  const weeklyCompletionData = habits.map(habit => {
    const daysCompleted = last7Days.filter(day => habit.completedDates.includes(day)).length;
    return {
      id: habit.id,
      name: habit.name,
      icon: habit.icon,
      color: habit.color,
      completedDays: daysCompleted,
      rate: Math.round((daysCompleted / 7) * 100)
    };
  });

  // Sort habits by completion rate (highest first)
  weeklyCompletionData.sort((a, b) => b.rate - a.rate);

  return (
    <GradientBackground variant="secondary" intensity="subtle">
      <ScrollView style={styles.container}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          <ThemedView style={styles.header}>
            <ThemedText type="title" style={styles.title}>Your Progress</ThemedText>
          </ThemedView>

          <ThemedView style={styles.statsContainer}>
            <StatsCard delay={0}>
              <ThemedText style={styles.statValue}>{completionRate}%</ThemedText>
              <ThemedText style={styles.statLabel}>Today's Completion</ThemedText>
            </StatsCard>

            <StatsCard delay={100}>
              <ThemedText style={styles.statValue}>
                {longestStreakHabit ? longestStreakHabit.streak : 0}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {longestStreakHabit ? `${longestStreakHabit.name} Streak` : 'No Streaks Yet'}
              </ThemedText>
            </StatsCard>
          </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Weekly Overview</ThemedText>

          {weeklyCompletionData.length === 0 ? (
            <ThemedView style={styles.emptyState}>
              <ThemedText style={styles.emptyStateText}>
                No habit data available yet. Start by creating a habit!
              </ThemedText>
            </ThemedView>
          ) : (
            weeklyCompletionData.map((habit, index) => (
              <HabitProgressCard key={habit.id} delay={index * 100}>
                <View style={styles.habitProgressContent}>
                  <View style={[styles.habitIcon, { backgroundColor: `${habit.color}20` }]}>
                    <ThemedText>{habit.icon}</ThemedText>
                  </View>
                  <View style={styles.habitDetails}>
                    <ThemedText style={styles.habitName}>{habit.name}</ThemedText>
                    <View style={styles.progressBarContainer}>
                      <View
                        style={[
                          styles.progressBarFill,
                          { width: `${habit.rate}%`, backgroundColor: habit.color }
                        ]}
                      />
                    </View>
                    <ThemedText style={styles.habitStat}>
                      {habit.completedDays}/7 days • {habit.rate}% complete
                    </ThemedText>
                  </View>
                </View>
              </HabitProgressCard>
            ))
          )}
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Monthly Summary</ThemedText>

          {habits.length === 0 ? (
            <ThemedView style={styles.emptyState}>
              <ThemedText style={styles.emptyStateText}>
                Create habits to see your monthly progress!
              </ThemedText>
            </ThemedView>
          ) : (
            <ThemedView style={styles.monthlySummary}>
              <ThemedText style={styles.summaryText}>
                You've maintained {habits.filter(h => h.streak >= 30).length} habits for 30+ days.
              </ThemedText>
              <ThemedText style={styles.summaryText}>
                {habits.filter(h => h.streak >= 7).length} habits have a streak of 7+ days.
              </ThemedText>
              <ThemedText style={styles.summaryText}>
                Keep going to build lasting habits!
              </ThemedText>
            </ThemedView>
          )}
        </ThemedView>
      </Animated.View>
    </ScrollView>
    </GradientBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 60,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 6,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  habitProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 12,
    padding: 12,
  },
  habitProgressContent: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  habitIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  habitDetails: {
    flex: 1,
  },
  habitName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 6,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 3,
    marginBottom: 6,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  habitStat: {
    fontSize: 12,
    opacity: 0.7,
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
  },
  emptyStateText: {
    textAlign: 'center',
    opacity: 0.7,
  },
  monthlySummary: {
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    padding: 16,
  },
  summaryText: {
    fontSize: 16,
    marginBottom: 8,
  },
});