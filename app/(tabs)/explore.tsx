import { CelebrationEffect } from '@/components/CelebrationEffect';
import { <PERSON><PERSON><PERSON> } from '@/components/Mascot';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useHabits } from '@/hooks/useHabits';
import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Animated, Easing, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';

// Helper function to determine if a color is light
const isColorLight = (color: string): boolean => {
  if (!color) return false; // handle cases where color might be undefined or empty
  const hex = color.replace('#', '');
  if (hex.length === 3) {
    const r_hex = hex.substring(0, 1).repeat(2);
    const g_hex = hex.substring(1, 2).repeat(2);
    const b_hex = hex.substring(2, 3).repeat(2);
    const r = parseInt(r_hex, 16);
    const g = parseInt(g_hex, 16);
    const b = parseInt(b_hex, 16);
    const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
    return luminance > 160;
  }
  if (hex.length !== 6) {
    // default to dark if color format is unexpected
    return false; 
  }
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  // Calculate luminance
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
  return luminance > 160; // Adjusted threshold for better balance
};

const COLORS = [
  '#FF6347', // Tomato
  '#4682B4', // Steel Blue
  '#32CD32', // Lime Green
  '#FF8C00', // Dark Orange
  '#8A2BE2', // Blue Violet
  '#20B2AA', // Light Sea Green
  '#FF69B4', // Hot Pink
  '#1E90FF', // Dodger Blue
];

const ICONS = ['🧘‍♂️', '💧', '📝', '🏃‍♂️', '🥗', '💤', '📚', '🧠', '🎨', '🌱', '🧹', '🧼'];

export default function CreateHabitScreen() {
  const router = useRouter();
  const { addHabit } = useHabits();
  
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [goalCount, setGoalCount] = useState('1');
  const [frequency, setFrequency] = useState<'daily' | 'weekly'>('daily');
  const [showCelebration, setShowCelebration] = useState(false);
  const [mascotMessage, setMascotMessage] = useState('Let\'s create a new habit to track!');
  const [showMascotMessage, setShowMascotMessage] = useState(true);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const iconScrollAnim = useRef(new Animated.Value(0)).current;
  const colorScrollAnim = useRef(new Animated.Value(0)).current;
  const createButtonAnim = useRef(new Animated.Value(0)).current;
  
  // Animate screen entry
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
    
    // Subtle animations for icon and color rows
    Animated.loop(
      Animated.sequence([
        Animated.timing(iconScrollAnim, {
          toValue: 1,
          duration: 30000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(iconScrollAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    Animated.loop(
      Animated.sequence([
        Animated.timing(colorScrollAnim, {
          toValue: -1,
          duration: 40000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(colorScrollAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Subtle pulsing for create button
    Animated.loop(
      Animated.sequence([
        Animated.timing(createButtonAnim, {
          toValue: 1,
          duration: 1500,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(createButtonAnim, {
          toValue: 0,
          duration: 1500,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Hide mascot message after a delay
    const timer = setTimeout(() => {
      setShowMascotMessage(false);
    }, 3000);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Update mascot message based on form progress
  useEffect(() => {
    if (name && !selectedIcon && !selectedColor) {
      setMascotMessage(`"${name}" sounds like a great habit to build!`);
      setShowMascotMessage(true);
      
      const timer = setTimeout(() => {
        setShowMascotMessage(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
    
    if (name && selectedIcon && !selectedColor) {
      setMascotMessage(`Great choice with ${selectedIcon}! Now pick a color.`);
      setShowMascotMessage(true);
      
      const timer = setTimeout(() => {
        setShowMascotMessage(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [name, selectedIcon, selectedColor]);
  
  const handleCreateHabit = async () => {
    if (!name) {
      Alert.alert('Missing Information', 'Please enter a habit name');
      setMascotMessage('Don\'t forget to give your habit a name!');
      setShowMascotMessage(true);
      return;
    }
    
    if (!selectedIcon) {
      Alert.alert('Missing Information', 'Please select an icon');
      setMascotMessage('Pick an icon that represents your habit!');
      setShowMascotMessage(true);
      return;
    }
    
    if (!selectedColor) {
      Alert.alert('Missing Information', 'Please select a color');
      setMascotMessage('Choose a color to personalize your habit!');
      setShowMascotMessage(true);
      return;
    }
    
    const goal = parseInt(goalCount, 10) || 1;
    
    await addHabit({
      name,
      description,
      icon: selectedIcon,
      color: selectedColor,
      frequency,
      goal,
      progress: 0,
    });
    
    // Show success celebration
    setMascotMessage(`Yay! "${name}" has been added to your habits!`);
    setShowMascotMessage(true);
    setShowCelebration(true);
    
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    // Wait for celebration to finish before navigating
    setTimeout(() => {
      // Reset form
      setName('');
      setDescription('');
      setSelectedIcon('');
      setSelectedColor('');
      setGoalCount('1');
      setFrequency('daily');
      setShowCelebration(false);
      
      // Navigate back to home
      router.push('/');
    }, 2000);
  };
  
  const buttonScale = createButtonAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.05],
  });
  
  const iconTranslate = iconScrollAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -100],
  });
  
  const colorTranslate = colorScrollAnim.interpolate({
    inputRange: [-1, 0],
    outputRange: [100, 0],
  });
  
  const getMascotMood = () => {
    if (showCelebration) return 'excited';
    if (name && selectedIcon && selectedColor) return 'happy';
    if (name || selectedIcon) return 'curious';
    return 'neutral';
  };
  
  // Create animated scroll views
  const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);
  
  return (
    <ScrollView style={styles.container}>
      <Animated.View style={[
        styles.content,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <ThemedView style={styles.header}>
          <Mascot
            mood={getMascotMood()}
            message={mascotMessage}
            showMessage={showMascotMessage}
            size={100}
            personality="playful"
            onPress={() => setShowMascotMessage(!showMascotMessage)}
          />
        </ThemedView>
        
        <ThemedView style={styles.formContainer}>
          <ThemedText style={styles.title}>Create New Habit</ThemedText>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Habit Name</ThemedText>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (text && !showMascotMessage) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }
              }}
              placeholder="e.g., Meditate"
              placeholderTextColor="#999"
            />
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Description (Optional)</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="e.g., Take 5 minutes to meditate each day"
              placeholderTextColor="#999"
              multiline
              numberOfLines={3}
            />
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Choose an Icon</ThemedText>
            <Animated.View style={{ transform: [{ translateX: iconTranslate }] }}>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false} 
                style={styles.iconContainer}
                contentContainerStyle={styles.iconContentContainer}
              >
                {ICONS.map((icon) => (
                  <TouchableOpacity
                    key={icon}
                    style={[
                      styles.iconOption,
                      selectedIcon === icon && { 
                        backgroundColor: selectedColor || '#ddd',
                        transform: [{ scale: 1.1 }]
                      },
                    ]}
                    onPress={() => {
                      setSelectedIcon(icon);
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      if (!showMascotMessage) {
                        setMascotMessage(`Great choice! ${icon} is perfect for this habit.`);
                        setShowMascotMessage(true);
                        setTimeout(() => setShowMascotMessage(false), 2000);
                      }
                    }}
                  >
                    <ThemedText style={styles.iconText}>{icon}</ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </Animated.View>
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Choose a Color</ThemedText>
            <Animated.View style={{ transform: [{ translateX: colorTranslate }] }}>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false} 
                style={styles.colorContainer}
                contentContainerStyle={styles.colorContentContainer}
              >
                {COLORS.map((color) => (
                  <TouchableOpacity
                    key={color}
                    style={[
                      styles.colorOption,
                      { backgroundColor: color },
                      selectedColor === color && styles.selectedColor,
                    ]}
                    onPress={() => {
                      setSelectedColor(color);
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    }}
                  />
                ))}
              </ScrollView>
            </Animated.View>
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Frequency</ThemedText>
            <ThemedView style={styles.frequencyContainer}>
              <TouchableOpacity
                style={[
                  styles.frequencyOption,
                  frequency === 'daily' && [styles.selectedFrequency, { backgroundColor: selectedColor || '#ddd' }],
                ]}
                onPress={() => {
                  setFrequency('daily');
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <ThemedText style={[
                  styles.frequencyText,
                  frequency === 'daily' && { color: selectedColor && isColorLight(selectedColor) ? '#000' : '#fff' }
                ]}>Daily</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.frequencyOption,
                  frequency === 'weekly' && [styles.selectedFrequency, { backgroundColor: selectedColor || '#ddd' }],
                ]}
                onPress={() => {
                  setFrequency('weekly');
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <ThemedText style={[
                  styles.frequencyText,
                  frequency === 'weekly' && { color: selectedColor && isColorLight(selectedColor) ? '#000' : '#fff' }
                ]}>Weekly</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>
          
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Goal Count (per {frequency === 'daily' ? 'day' : 'week'})</ThemedText>
            <View style={styles.goalInputContainer}>
              <TouchableOpacity
                style={styles.goalButton}
                onPress={() => {
                  const current = parseInt(goalCount, 10) || 1;
                  if (current > 1) {
                    setGoalCount((current - 1).toString());
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                }}
              >
                <ThemedText style={styles.goalButtonText}>-</ThemedText>
              </TouchableOpacity>
              
              <TextInput
                style={[styles.input, styles.goalInput]}
                value={goalCount}
                onChangeText={setGoalCount}
                keyboardType="number-pad"
                placeholder="1"
                placeholderTextColor="#999"
              />
              
              <TouchableOpacity
                style={styles.goalButton}
                onPress={() => {
                  const current = parseInt(goalCount, 10) || 1;
                  setGoalCount((current + 1).toString());
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <ThemedText style={styles.goalButtonText}>+</ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>
          
          <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
            <TouchableOpacity
              style={[styles.createButton, { backgroundColor: selectedColor || '#1E90FF' }]}
              onPress={handleCreateHabit}
            >
              <ThemedText style={[styles.createButtonText, {color: selectedColor && isColorLight(selectedColor) ? '#000' : '#fff'}]}>Create Habit</ThemedText>
            </TouchableOpacity>
          </Animated.View>
        </ThemedView>
      </Animated.View>
      
      <CelebrationEffect 
        visible={showCelebration} 
        type="sparkles" 
        intensity="medium"
        sound={true}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  formContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  iconContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  iconContentContainer: {
    paddingRight: 50,
  },
  iconOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  iconText: {
    fontSize: 24,
  },
  colorContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  colorContentContainer: {
    paddingRight: 50,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedColor: {
    borderWidth: 3,
    borderColor: '#000',
    transform: [{ scale: 1.1 }],
  },
  frequencyContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  frequencyOption: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedFrequency: {
    borderColor: 'transparent',
  },
  frequencyText: {
    fontWeight: '500',
  },
  goalInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  goalInput: {
    width: '30%',
    textAlign: 'center',
    marginHorizontal: 10,
  },
  goalButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  goalButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  createButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  createButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
});
