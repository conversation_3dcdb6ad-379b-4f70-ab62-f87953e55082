import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useHabits } from '@/hooks/useHabits';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import React, { useEffect, useState } from 'react';
import { Alert, Animated, Easing, Linking, ScrollView, StyleSheet, Switch, TouchableOpacity, View } from 'react-native';

interface SettingsSectionProps {
  title: string;
  children: React.ReactNode;
}

const SettingsSection: React.FC<SettingsSectionProps> = ({ title, children }) => (
  <ThemedView style={styles.section}>
    <ThemedText style={styles.sectionTitle}>{title}</ThemedText>
    <ThemedView style={styles.sectionContent}>
      {children}
    </ThemedView>
  </ThemedView>
);

interface SettingsItemProps {
  title: string;
  description?: string;
  icon?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
}

const SettingsItem: React.FC<SettingsItemProps> = ({ 
  title, 
  description, 
  icon, 
  onPress, 
  rightElement 
}) => (
  <TouchableOpacity 
    style={styles.settingsItem} 
    onPress={() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      if (onPress) onPress();
    }}
    disabled={!onPress}
  >
    {icon && (
      <ThemedView style={styles.settingsItemIcon}>
        <ThemedText style={styles.iconText}>{icon}</ThemedText>
      </ThemedView>
    )}
    <View style={styles.settingsItemContent}>
      <ThemedText style={styles.settingsItemTitle}>{title}</ThemedText>
      {description && (
        <ThemedText style={styles.settingsItemDescription}>{description}</ThemedText>
      )}
    </View>
    {rightElement}
  </TouchableOpacity>
);

export default function SettingsScreen() {
  const { resetTodayHabits } = useHabits();
  const colorScheme = useColorScheme();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [hapticFeedback, setHapticFeedback] = useState(true);
  const [soundEffects, setSoundEffects] = useState(true);
  
  // Animate screen entry
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start();
    
    // Load settings from storage
    const loadSettings = async () => {
      try {
        const notifSetting = await AsyncStorage.getItem('setting_notifications');
        const hapticSetting = await AsyncStorage.getItem('setting_haptics');
        const soundSetting = await AsyncStorage.getItem('setting_sounds');
        
        if (notifSetting !== null) setNotificationsEnabled(notifSetting === 'true');
        if (hapticSetting !== null) setHapticFeedback(hapticSetting === 'true');
        if (soundSetting !== null) setSoundEffects(soundSetting === 'true');
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    };
    
    loadSettings();
  }, []);
  
  // Save settings when changed
  const saveSetting = async (key: string, value: boolean) => {
    try {
      await AsyncStorage.setItem(`setting_${key}`, value.toString());
    } catch (error) {
      console.error(`Failed to save ${key} setting:`, error);
    }
  };
  
  const handleResetToday = () => {
    Alert.alert(
      'Reset Today\'s Progress',
      'Are you sure you want to reset all habits for today? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: async () => {
            await resetTodayHabits();
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        }
      ]
    );
  };
  
  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'Are you sure you want to clear all habits and progress data? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear Everything', 
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.clear();
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              Alert.alert('Success', 'All data has been cleared. Please restart the app.');
            } catch (error) {
              console.error('Failed to clear data:', error);
              Alert.alert('Error', 'Failed to clear data. Please try again.');
            }
          }
        }
      ]
    );
  };
  
  const handleExportData = () => {
    Alert.alert(
      'Export Data',
      'This feature will be available in a future update.',
      [{ text: 'OK' }]
    );
  };
  
  const handleImportData = () => {
    Alert.alert(
      'Import Data',
      'This feature will be available in a future update.',
      [{ text: 'OK' }]
    );
  };
  
  return (
    <ScrollView style={styles.container}>
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Settings</ThemedText>
        </ThemedView>
        
        <SettingsSection title="Appearance">
          <SettingsItem 
            title="Theme" 
            description={`Currently using ${colorScheme === 'dark' ? 'Dark' : 'Light'} theme`}
            icon="🎨"
            rightElement={
              <ThemedText style={styles.valueText}>
                {colorScheme === 'dark' ? 'Dark' : 'Light'}
              </ThemedText>
            }
          />
        </SettingsSection>
        
        <SettingsSection title="Notifications">
          <SettingsItem 
            title="Enable Notifications" 
            description="Get reminders for your habits"
            icon="🔔"
            rightElement={
              <Switch 
                value={notificationsEnabled}
                onValueChange={(value) => {
                  setNotificationsEnabled(value);
                  saveSetting('notifications', value);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              />
            }
          />
        </SettingsSection>
        
        <SettingsSection title="Feedback">
          <SettingsItem 
            title="Haptic Feedback" 
            description="Vibration when interacting with the app"
            icon="📳"
            rightElement={
              <Switch 
                value={hapticFeedback}
                onValueChange={(value) => {
                  setHapticFeedback(value);
                  saveSetting('haptics', value);
                  if (value) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              />
            }
          />
          
          <SettingsItem 
            title="Sound Effects" 
            description="Play sounds for celebrations and achievements"
            icon="🔊"
            rightElement={
              <Switch 
                value={soundEffects}
                onValueChange={(value) => {
                  setSoundEffects(value);
                  saveSetting('sounds', value);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              />
            }
          />
        </SettingsSection>
        
        <SettingsSection title="Data Management">
          <SettingsItem 
            title="Reset Today's Progress" 
            description="Mark all habits as incomplete for today"
            icon="🔄"
            onPress={handleResetToday}
          />
          
          <SettingsItem 
            title="Export Data" 
            description="Save your habits and progress"
            icon="📤"
            onPress={handleExportData}
          />
          
          <SettingsItem 
            title="Import Data" 
            description="Restore from a backup"
            icon="📥"
            onPress={handleImportData}
          />
          
          <SettingsItem 
            title="Clear All Data" 
            description="Delete all habits and progress"
            icon="🗑️"
            onPress={handleClearData}
          />
        </SettingsSection>
        
        <SettingsSection title="About">
          <SettingsItem 
            title="Version" 
            description="1.0.0"
            icon="📱"
          />
          
          <SettingsItem 
            title="Privacy Policy" 
            description="Read our privacy policy"
            icon="🔒"
            onPress={() => Linking.openURL('https://example.com/privacy')}
          />
          
          <SettingsItem 
            title="Terms of Service" 
            description="Read our terms of service"
            icon="📄"
            onPress={() => Linking.openURL('https://example.com/terms')}
          />
          
          <SettingsItem 
            title="Send Feedback" 
            description="Help us improve the app"
            icon="📧"
            onPress={() => Linking.openURL('mailto:<EMAIL>')}
          />
        </SettingsSection>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 60,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    opacity: 0.8,
  },
  sectionContent: {
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  settingsItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  iconText: {
    fontSize: 20,
  },
  settingsItemContent: {
    flex: 1,
  },
  settingsItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingsItemDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
  valueText: {
    fontSize: 16,
    opacity: 0.7,
    marginLeft: 8,
  },
}); 