import { Tabs } from 'expo-router';
import React from 'react';
import { Platform, Text } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { HabitsProvider } from '@/hooks/useHabits';

export default function TabLayout() {
  const colorScheme = useColorScheme();

  // Placeholder for TabBarIcon
  const PlaceholderIcon = ({ name, color }: { name: string; color: string }) => (
    <Text style={{ color: color, fontSize: 18 }}>{name.charAt(0).toUpperCase()}</Text>
  );

  return (
    <HabitsProvider>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
          headerShown: false,
          tabBarButton: HapticTab,
          tabBarBackground: TabBarBackground,
          tabBarStyle: Platform.select({
            ios: {
              // Use a transparent background on iOS to show the blur effect
              position: 'absolute',
            },
            default: {},
          }),
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            tabBarIcon: ({ color, focused }) => (
              <PlaceholderIcon name={focused ? 'Home' : 'Home'} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="explore"
          options={{
            title: 'Create Habit',
            tabBarIcon: ({ color, focused }) => (
              <PlaceholderIcon name={focused ? 'Add' : 'Add'} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="progress"
          options={{
            title: 'Progress',
            tabBarIcon: ({ color, focused }) => (
              <PlaceholderIcon name={focused ? 'Stats' : 'Stats'} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="community"
          options={{
            title: 'Community',
            tabBarIcon: ({ color, focused }) => (
              <PlaceholderIcon name={focused ? 'People' : 'People'} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: 'Settings',
            tabBarIcon: ({ color, focused }) => (
              <PlaceholderIcon name={focused ? 'Settings' : 'Settings'} color={color} />
            ),
          }}
        />
      </Tabs>
    </HabitsProvider>
  );
}
