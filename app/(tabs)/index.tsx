import { CelebrationEffect } from '@/components/CelebrationEffect';
import { HabitCard } from '@/components/HabitCard';
import { Mascot, MascotMood, MascotPersonality } from '@/components/Mascot';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { GradientBackground } from '@/components/ui/GradientBackground';
import { LoadingScreen } from '@/components/ui/LoadingScreen';
import { useHabits } from '@/hooks/useHabits';
import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Easing, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

export default function HomeScreen() {
  const router = useRouter();
  const {
    habits,
    loading,
    completeHabit,
    celebratingHabitId,
    setCelebratingHabitId,
    streakMilestone,
    setStreakMilestone
  } = useHabits();

  const [mascotMood, setMascotMood] = useState<MascotMood>('neutral');
  const [mascotPersonality, setMascotPersonality] = useState<MascotPersonality>('supportive');
  const [mascotMessage, setMascotMessage] = useState('');
  const [showMessage, setShowMessage] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const [celebrationType, setCelebrationType] = useState<'confetti' | 'sparkles' | 'stars' | 'achievement'>('confetti');
  const [celebrationIntensity, setCelebrationIntensity] = useState<'small' | 'medium' | 'large'>('medium');

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  // Animate screen entry
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Calculate completion percentage for today
  const completedCount = habits.filter(h => h.completedToday).length;
  const completionPercentage = habits.length > 0
    ? Math.round((completedCount / habits.length) * 100)
    : 0;

  // Get the highest streak
  const highestStreak = habits.reduce((max, habit) => Math.max(max, habit.streak), 0);

  // Update mascot mood based on completion and time of day
  useEffect(() => {
    const hour = new Date().getHours();
    let timeBasedMood: MascotMood = 'neutral';
    let timeBasedMessage = '';

    // Time-based greeting
    if (hour < 6) {
      timeBasedMood = 'sleeping';
      timeBasedMessage = 'Shh... it\'s early. Let\'s start the day mindfully.';
    } else if (hour < 12) {
      timeBasedMood = 'happy';
      timeBasedMessage = 'Good morning! Ready for a great day?';
    } else if (hour < 18) {
      timeBasedMood = 'neutral';
      timeBasedMessage = 'How\'s your day going? Keep up the good work!';
    } else {
      timeBasedMood = 'encouraging';
      timeBasedMessage = 'Good evening! Let\'s finish strong today.';
    }

    // Progress-based mood overrides time-based mood if significant progress
    if (completionPercentage === 100) {
      setMascotMood('excited');
      setMascotMessage('Amazing job! You completed all your habits today!');
      setMascotPersonality('energetic');
    } else if (completionPercentage >= 70) {
      setMascotMood('happy');
      setMascotMessage('You\'re doing great! Keep it up!');
      setMascotPersonality('playful');
    } else if (completionPercentage >= 30) {
      setMascotMood('neutral');
      setMascotMessage('You\'re making progress! Keep going!');
      setMascotPersonality('supportive');
    } else if (completionPercentage > 0) {
      setMascotMood('neutral');
      setMascotMessage('Every small step counts!');
      setMascotPersonality('supportive');
    } else {
      setMascotMood(timeBasedMood);
      setMascotMessage(timeBasedMessage);
      setMascotPersonality('calm');
    }

    // Show message briefly on first load
    if (!showMessage) {
      setShowMessage(true);
      const timer = setTimeout(() => {
        setShowMessage(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [completionPercentage]);

  // Handle celebration effects
  useEffect(() => {
    if (celebratingHabitId) {
      setShowCelebration(true);
      setCelebrationType('confetti');
      setCelebrationIntensity('medium');

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Update mascot to be happy about the completion
      setMascotMood('happy');

      // Find the completed habit to customize message
      const completedHabit = habits.find(h => h.id === celebratingHabitId);
      if (completedHabit) {
        setMascotMessage(`Great job completing ${completedHabit.name}!`);
      }

      setShowMessage(true);

      // Reset the celebrating state after animation completes
      const timer = setTimeout(() => {
        setShowCelebration(false);
        setCelebratingHabitId(null);

        // Hide message after a delay
        setTimeout(() => {
          setShowMessage(false);
        }, 1500);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [celebratingHabitId, habits]);

  // Handle streak milestone celebrations
  useEffect(() => {
    if (streakMilestone) {
      setMascotMood('excited');
      setMascotPersonality('energetic');
      setMascotMessage(`Wow! You've reached a ${streakMilestone}-day streak! That's incredible!`);
      setShowMessage(true);

      // Special celebration for milestones
      setShowCelebration(true);
      setCelebrationType('achievement');
      setCelebrationIntensity('large');

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Reset after showing message
      const timer = setTimeout(() => {
        setShowCelebration(false);

        setTimeout(() => {
          setStreakMilestone(null);
          setShowMessage(false);
        }, 2000);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [streakMilestone]);

  const handleMascotPress = () => {
    setShowMessage(!showMessage);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Change mascot mood to curious when pressed
    if (!showMessage) {
      setMascotMood('curious');
    }
  };

  const handleHabitPress = (habitId: string) => {
    // Use index route instead of dynamic route for now
    router.push('/');
  };

  const handleHabitComplete = (habitId: string) => {
    completeHabit(habitId);
  };

  if (loading) {
    return (
      <LoadingScreen
        message="Loading your mindful journey..."
        showSkeleton={true}
        type="pulse"
      />
    );
  }

  return (
    <GradientBackground variant="primary" intensity="subtle">
      <ScrollView style={styles.container}>
        <Animated.View style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [
              { translateY: slideAnim },
              { scale: scaleAnim }
            ]
          }
        ]}>
        <ThemedView style={styles.header}>
          <Mascot
            mood={mascotMood}
            message={mascotMessage}
            showMessage={showMessage}
            size={120}
            onPress={handleMascotPress}
            personality={mascotPersonality}
            streakCount={highestStreak}
          />

          <ThemedView style={styles.statsContainer}>
            <ThemedView style={styles.statItem}>
              <ThemedText style={styles.statValue}>{completedCount}/{habits.length}</ThemedText>
              <ThemedText style={styles.statLabel}>Completed</ThemedText>
            </ThemedView>

            <ThemedView style={[styles.statItem, styles.progressStatItem]}>
              <View style={styles.progressBarContainer}>
                <View
                  style={[
                    styles.progressBarFill,
                    {
                      width: `${completionPercentage}%`,
                      backgroundColor: getProgressColor(completionPercentage).background
                    }
                  ]}
                />
              </View>
              <ThemedText style={[styles.statValue, { color: getProgressColor(completionPercentage).text }]}>
                {completionPercentage}%
              </ThemedText>
              <ThemedText style={styles.statLabel}>Progress</ThemedText>
            </ThemedView>

            <ThemedView style={styles.statItem}>
              <ThemedText style={styles.statValue}>
                {highestStreak}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Best Streak</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.habitSection}>
          <ThemedText style={styles.sectionTitle}>Today's Habits</ThemedText>

          {habits.length === 0 ? (
            <ThemedView style={styles.emptyState}>
              <ThemedText style={styles.emptyStateIcon}>👋</ThemedText>
              <ThemedText style={styles.emptyStateTitle}>Welcome to Mindful Companion!</ThemedText>
              <ThemedText style={styles.emptyStateText}>
                You don't have any habits yet. Let's get started by creating your first habit!
              </ThemedText>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={() => router.push('/explore')}
              >
                <ThemedText style={styles.emptyStateButtonText}>Create Your First Habit</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          ) : (
            habits.map(habit => (
              <HabitCard
                key={habit.id}
                habit={habit}
                onPress={() => handleHabitPress(habit.id)}
                onComplete={() => handleHabitComplete(habit.id)}
              />
            ))
          )}
        </ThemedView>
      </Animated.View>

        <CelebrationEffect
          visible={showCelebration}
          type={celebrationType}
          intensity={celebrationIntensity}
          sound={true}
        />
      </ScrollView>
    </GradientBackground>
  );
}

// Helper function to get color based on progress percentage
interface ProgressColorInfo {
  background: string;
  text: string;
}

const getProgressColor = (percentage: number): ProgressColorInfo => {
  if (percentage >= 100) return { background: '#4CAF50', text: '#FFFFFF' }; // Green
  if (percentage >= 70) return { background: '#8BC34A', text: '#000000' };  // Light Green
  if (percentage >= 50) return { background: '#CDDC39', text: '#000000' };  // Lime
  if (percentage >= 30) return { background: '#FFEB3B', text: '#000000' };  // Yellow
  return { background: '#FFC107', text: '#000000' };                        // Amber
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    minHeight: 220,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
    paddingBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 20,
  },
  statItem: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    minWidth: 80,
  },
  progressStatItem: {
    width: 120,
  },
  progressBarContainer: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 3,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  habitSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    height: 300,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.03)',
    marginVertical: 20,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyStateText: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  emptyStateButton: {
    backgroundColor: '#0a7ea4',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});
