import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useHabits } from '@/hooks/useHabits';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import React, { useEffect, useState } from 'react';
import { Alert, Animated, Easing, Image, Linking, ScrollView, Share, StyleSheet, TouchableOpacity } from 'react-native';

// Placeholder avatar images
const AVATARS = [
  'https://i.pravatar.cc/150?img=1',
  'https://i.pravatar.cc/150?img=2',
  'https://i.pravatar.cc/150?img=3',
  'https://i.pravatar.cc/150?img=4',
  'https://i.pravatar.cc/150?img=5',
  'https://i.pravatar.cc/150?img=6',
  'https://i.pravatar.cc/150?img=7',
  'https://i.pravatar.cc/150?img=8',
];

// Placeholder user data
interface User {
  id: string;
  name: string;
  avatar: string;
  score: number;
  streak: number;
}

// Placeholder challenge data
interface Challenge {
  id: string;
  title: string;
  description: string;
  icon: string;
  participants: number;
  daysLeft: number;
  joined: boolean;
}

export default function CommunityScreen() {
  const { habits } = useHabits();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [userName, setUserName] = useState('You');
  const [userAvatar, setUserAvatar] = useState(AVATARS[0]);
  const [leaderboard, setLeaderboard] = useState<User[]>([]);
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  
  // Animate screen entry
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start();
    
    // Load user profile from storage
    const loadUserProfile = async () => {
      try {
        const storedName = await AsyncStorage.getItem('user_name');
        const storedAvatar = await AsyncStorage.getItem('user_avatar');
        
        if (storedName) setUserName(storedName);
        if (storedAvatar) setUserAvatar(storedAvatar);
      } catch (error) {
        console.error('Failed to load user profile:', error);
      }
    };
    
    loadUserProfile();
    generateLeaderboard();
    generateChallenges();
  }, []);
  
  // Generate placeholder leaderboard data
  const generateLeaderboard = () => {
    // Calculate user score based on habits
    const userScore = habits.reduce((total, habit) => total + habit.streak, 0);
    const highestStreak = habits.reduce((max, habit) => Math.max(max, habit.streak), 0);
    
    // Create user entry
    const userEntry: User = {
      id: 'user',
      name: userName,
      avatar: userAvatar,
      score: userScore,
      streak: highestStreak,
    };
    
    // Generate placeholder competitors
    const competitors: User[] = [
      {
        id: '1',
        name: 'Alex',
        avatar: AVATARS[1],
        score: Math.floor(Math.random() * 50) + 20,
        streak: Math.floor(Math.random() * 14) + 3,
      },
      {
        id: '2',
        name: 'Jamie',
        avatar: AVATARS[2],
        score: Math.floor(Math.random() * 50) + 20,
        streak: Math.floor(Math.random() * 14) + 3,
      },
      {
        id: '3',
        name: 'Taylor',
        avatar: AVATARS[3],
        score: Math.floor(Math.random() * 50) + 20,
        streak: Math.floor(Math.random() * 14) + 3,
      },
      {
        id: '4',
        name: 'Jordan',
        avatar: AVATARS[4],
        score: Math.floor(Math.random() * 50) + 20,
        streak: Math.floor(Math.random() * 14) + 3,
      },
    ];
    
    // Combine and sort by score
    const combined = [userEntry, ...competitors];
    combined.sort((a, b) => b.score - a.score);
    
    setLeaderboard(combined);
  };
  
  // Generate placeholder challenges
  const generateChallenges = async () => {
    try {
      // Try to load joined challenges from storage
      const joinedChallenges = await AsyncStorage.getItem('joined_challenges');
      const joinedIds = joinedChallenges ? JSON.parse(joinedChallenges) : [];
      
      const challengesList: Challenge[] = [
        {
          id: '1',
          title: '7-Day Meditation',
          description: 'Meditate for at least 5 minutes every day for a week',
          icon: '🧘‍♂️',
          participants: 245,
          daysLeft: 3,
          joined: joinedIds.includes('1'),
        },
        {
          id: '2',
          title: 'Hydration Hero',
          description: 'Drink 8 glasses of water daily for 5 days',
          icon: '💧',
          participants: 189,
          daysLeft: 5,
          joined: joinedIds.includes('2'),
        },
        {
          id: '3',
          title: 'Digital Detox',
          description: 'Limit screen time to 2 hours per day for 3 days',
          icon: '📵',
          participants: 132,
          daysLeft: 2,
          joined: joinedIds.includes('3'),
        },
        {
          id: '4',
          title: '10k Steps',
          description: 'Walk 10,000 steps every day for a week',
          icon: '👣',
          participants: 278,
          daysLeft: 7,
          joined: joinedIds.includes('4'),
        },
      ];
      
      setChallenges(challengesList);
    } catch (error) {
      console.error('Failed to load challenges:', error);
    }
  };
  
  const handleJoinChallenge = async (challenge: Challenge) => {
    try {
      // Toggle joined status
      const updatedChallenge = { ...challenge, joined: !challenge.joined };
      
      // Update challenges list
      setChallenges(challenges.map(c => 
        c.id === challenge.id ? updatedChallenge : c
      ));
      
      // Update storage
      const joinedChallenges = await AsyncStorage.getItem('joined_challenges');
      let joinedIds = joinedChallenges ? JSON.parse(joinedChallenges) : [];
      
      if (updatedChallenge.joined) {
        joinedIds.push(challenge.id);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        joinedIds = joinedIds.filter((id: string) => id !== challenge.id);
      }
      
      await AsyncStorage.setItem('joined_challenges', JSON.stringify(joinedIds));
    } catch (error) {
      console.error('Failed to join challenge:', error);
    }
  };
  
  const handleEditProfile = () => {
    Alert.prompt(
      'Edit Profile',
      'Enter your name:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save',
          onPress: async (name) => {
            if (name) {
              setUserName(name);
              await AsyncStorage.setItem('user_name', name);
              generateLeaderboard(); // Refresh leaderboard with new name
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            }
          }
        }
      ],
      'plain-text',
      userName
    );
  };
  
  const handleShareProgress = async () => {
    try {
      const message = `I'm building great habits with Mindful Companion! I have ${habits.length} habits and my longest streak is ${habits.reduce((max, habit) => Math.max(max, habit.streak), 0)} days. Join me!`;
      
      await Share.share({
        message,
        title: 'My Habit Progress',
      });
      
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>Community</ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.profileCard}>
          <Image 
            source={{ uri: userAvatar }} 
            style={styles.profileAvatar} 
          />
          <ThemedView style={styles.profileInfo}>
            <ThemedText style={styles.profileName}>{userName}</ThemedText>
            <ThemedText style={styles.profileStats}>
              {habits.length} habits • {habits.reduce((max, habit) => Math.max(max, habit.streak), 0)} day best streak
            </ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.profileActions}>
            <TouchableOpacity 
              style={styles.profileButton} 
              onPress={handleEditProfile}
            >
              <ThemedText style={styles.profileButtonText}>Edit</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.profileButton, styles.shareButton]} 
              onPress={handleShareProgress}
            >
              <ThemedText style={styles.profileButtonText}>Share</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
        
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Leaderboard</ThemedText>
          
          {leaderboard.length === 0 ? (
            <ThemedView style={styles.emptyState}>
              <ThemedText style={styles.emptyStateText}>
                Create habits to appear on the leaderboard!
              </ThemedText>
            </ThemedView>
          ) : (
            <ThemedView style={styles.leaderboard}>
              {leaderboard.map((user, index) => (
                <ThemedView 
                  key={user.id} 
                  style={[
                    styles.leaderboardItem,
                    user.id === 'user' && styles.leaderboardItemHighlighted
                  ]}
                >
                  <ThemedText style={styles.leaderboardRank}>{index + 1}</ThemedText>
                  <Image source={{ uri: user.avatar }} style={styles.leaderboardAvatar} />
                  <ThemedView style={styles.leaderboardUserInfo}>
                    <ThemedText style={styles.leaderboardName}>
                      {user.name} {user.id === 'user' && '(You)'}
                    </ThemedText>
                    <ThemedText style={styles.leaderboardScore}>
                      {user.score} points • {user.streak} day streak
                    </ThemedText>
                  </ThemedView>
                </ThemedView>
              ))}
            </ThemedView>
          )}
        </ThemedView>
        
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Challenges</ThemedText>
          
          <ThemedView style={styles.challenges}>
            {challenges.map((challenge) => (
              <ThemedView key={challenge.id} style={styles.challengeCard}>
                <ThemedView style={styles.challengeHeader}>
                  <ThemedText style={styles.challengeIcon}>{challenge.icon}</ThemedText>
                  <ThemedView style={styles.challengeHeaderInfo}>
                    <ThemedText style={styles.challengeTitle}>{challenge.title}</ThemedText>
                    <ThemedText style={styles.challengeMeta}>
                      {challenge.participants} participants • {challenge.daysLeft} days left
                    </ThemedText>
                  </ThemedView>
                </ThemedView>
                
                <ThemedText style={styles.challengeDescription}>
                  {challenge.description}
                </ThemedText>
                
                <TouchableOpacity 
                  style={[
                    styles.challengeButton,
                    challenge.joined && styles.challengeButtonJoined
                  ]} 
                  onPress={() => handleJoinChallenge(challenge)}
                >
                  <ThemedText style={[
                    styles.challengeButtonText,
                    challenge.joined && styles.challengeButtonTextJoined
                  ]}>
                    {challenge.joined ? 'Leave Challenge' : 'Join Challenge'}
                  </ThemedText>
                </TouchableOpacity>
              </ThemedView>
            ))}
          </ThemedView>
        </ThemedView>
        
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Connect</ThemedText>
          
          <ThemedView style={styles.connectOptions}>
            <TouchableOpacity 
              style={styles.connectButton}
              onPress={() => Linking.openURL('https://facebook.com')}
            >
              <ThemedText style={styles.connectButtonText}>Facebook</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.connectButton}
              onPress={() => Linking.openURL('https://twitter.com')}
            >
              <ThemedText style={styles.connectButtonText}>Twitter</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.connectButton}
              onPress={() => Linking.openURL('https://instagram.com')}
            >
              <ThemedText style={styles.connectButtonText}>Instagram</ThemedText>
            </TouchableOpacity>
          </ThemedView>
          
          <ThemedText style={styles.disclaimer}>
            Note: This is a demo app. Social features are simulated and no real data is shared.
          </ThemedText>
        </ThemedView>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 60,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  profileCard: {
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  profileInfo: {
    marginBottom: 16,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileStats: {
    fontSize: 14,
    opacity: 0.7,
  },
  profileActions: {
    flexDirection: 'row',
  },
  profileButton: {
    backgroundColor: '#0a7ea4',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  shareButton: {
    backgroundColor: '#4CAF50',
  },
  profileButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  leaderboard: {
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    overflow: 'hidden',
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  leaderboardItemHighlighted: {
    backgroundColor: 'rgba(10, 126, 164, 0.1)',
  },
  leaderboardRank: {
    fontSize: 18,
    fontWeight: 'bold',
    width: 30,
    textAlign: 'center',
  },
  leaderboardAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  leaderboardUserInfo: {
    flex: 1,
  },
  leaderboardName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  leaderboardScore: {
    fontSize: 14,
    opacity: 0.7,
  },
  challenges: {
    marginBottom: 16,
  },
  challengeCard: {
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  challengeHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  challengeIcon: {
    fontSize: 24,
    marginRight: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    width: 48,
    height: 48,
    borderRadius: 24,
    textAlign: 'center',
    textAlignVertical: 'center',
    overflow: 'hidden',
  },
  challengeHeaderInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  challengeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  challengeMeta: {
    fontSize: 14,
    opacity: 0.7,
  },
  challengeDescription: {
    fontSize: 16,
    marginBottom: 16,
  },
  challengeButton: {
    backgroundColor: '#0a7ea4',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  challengeButtonJoined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#0a7ea4',
  },
  challengeButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  challengeButtonTextJoined: {
    color: '#0a7ea4',
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
  },
  emptyStateText: {
    textAlign: 'center',
    opacity: 0.7,
  },
  connectOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  connectButton: {
    flex: 1,
    backgroundColor: '#0a7ea4',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  connectButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  disclaimer: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    opacity: 0.6,
  },
}); 