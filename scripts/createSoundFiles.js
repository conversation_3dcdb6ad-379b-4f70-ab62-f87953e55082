const fs = require('fs');
const path = require('path');
const https = require('https');

// Define sound file URLs - these are placeholder URLs
const SOUND_FILES = {
  'achievement.mp3': 'https://www.soundjay.com/buttons/sounds/button-09.mp3',
  'sparkle.mp3': 'https://www.soundjay.com/buttons/sounds/button-10.mp3',
  'pop.mp3': 'https://www.soundjay.com/buttons/sounds/button-1.mp3'
};

// Create sounds directory if it doesn't exist
const soundsDir = path.join(__dirname, '../assets/sounds');
if (!fs.existsSync(soundsDir)) {
  fs.mkdirSync(soundsDir, { recursive: true });
  console.log('Created sounds directory');
}

// Download each sound file
Object.entries(SOUND_FILES).forEach(([filename, url]) => {
  const filePath = path.join(soundsDir, filename);
  
  // Check if file already exists
  if (fs.existsSync(filePath)) {
    console.log(`${filename} already exists, skipping download`);
    return;
  }
  
  console.log(`Downloading ${filename} from ${url}...`);
  
  // Create a write stream
  const file = fs.createWriteStream(filePath);
  
  // Download the file
  https.get(url, (response) => {
    response.pipe(file);
    
    file.on('finish', () => {
      file.close();
      console.log(`Downloaded ${filename}`);
    });
  }).on('error', (err) => {
    // Handle errors
    fs.unlink(filePath, () => {}); // Delete the file on error
    console.error(`Error downloading ${filename}: ${err.message}`);
  });
});

console.log('Sound files setup complete!'); 