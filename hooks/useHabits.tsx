import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { Habit } from '../components/HabitCard';

interface HabitsContextType {
  habits: Habit[];
  loading: boolean;
  addHabit: (habit: Omit<Habit, 'id' | 'streak' | 'completedToday' | 'completedDates'>) => Promise<void>;
  completeHabit: (id: string) => Promise<void>;
  deleteHabit: (id: string) => Promise<void>;
  editHabit: (habit: Habit) => Promise<void>;
  resetTodayHabits: () => Promise<void>;
  celebratingHabitId: string | null;
  setCelebratingHabitId: (id: string | null) => void;
  streakMilestone: number | null;
  setStreakMilestone: (milestone: number | null) => void;
}

const HabitsContext = createContext<HabitsContextType | undefined>(undefined);

const STORAGE_KEY = 'mindful_companion_habits';
const LAST_CHECK_DATE_KEY = 'last_check_date';

export const HabitsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [habits, setHabits] = useState<Habit[]>([]);
  const [loading, setLoading] = useState(true);
  const [celebratingHabitId, setCelebratingHabitId] = useState<string | null>(null);
  const [streakMilestone, setStreakMilestone] = useState<number | null>(null);

  // Load habits from storage on mount
  useEffect(() => {
    const loadHabits = async () => {
      try {
        const storedHabits = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedHabits) {
          setHabits(JSON.parse(storedHabits));
        } else {
          // Initialize with empty habits array instead of mock data
          const emptyHabits: Habit[] = [];
          setHabits(emptyHabits);
          await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(emptyHabits));
        }

        // Check if we need to reset daily habits
        await checkAndResetHabits();
        
        setLoading(false);
      } catch (error) {
        console.error('Failed to load habits:', error);
        setLoading(false);
      }
    };

    loadHabits();
  }, []);

  // Save habits to storage whenever they change
  useEffect(() => {
    const saveHabits = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(habits));
      } catch (error) {
        console.error('Failed to save habits:', error);
      }
    };

    if (habits.length > 0 && !loading) {
      saveHabits();
    }
  }, [habits, loading]);

  // Check if we need to reset daily habits (new day)
  const checkAndResetHabits = async () => {
    try {
      const lastCheckDate = await AsyncStorage.getItem(LAST_CHECK_DATE_KEY);
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

      if (lastCheckDate !== today) {
        // It's a new day, reset completedToday flags
        const updatedHabits = habits.map(habit => ({
          ...habit,
          completedToday: false,
          progress: 0,
        }));
        
        setHabits(updatedHabits);
        await AsyncStorage.setItem(LAST_CHECK_DATE_KEY, today);
      }
    } catch (error) {
      console.error('Failed to check date:', error);
    }
  };

  const addHabit = async (habitData: Omit<Habit, 'id' | 'streak' | 'completedToday' | 'completedDates'>) => {
    const newHabit: Habit = {
      ...habitData,
      id: Date.now().toString(),
      streak: 0,
      completedToday: false,
      completedDates: [],
      progress: 0,
    };

    setHabits(prevHabits => [...prevHabits, newHabit]);
  };

  const completeHabit = async (id: string) => {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    setHabits(prevHabits => 
      prevHabits.map(habit => {
        if (habit.id === id) {
          const completedDates = [...habit.completedDates];
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = yesterday.toISOString().split('T')[0];
          
          // Check if we should increment the streak
          let newStreak = habit.streak;
          if (!completedDates.includes(today)) {
            if (completedDates.includes(yesterdayStr) || newStreak === 0) {
              newStreak += 1;
              
              // Check for milestone (3, 7, 14, 30, 50, 100 days)
              const milestones = [3, 7, 14, 30, 50, 100];
              if (milestones.includes(newStreak)) {
                setStreakMilestone(newStreak);
              }
            } else {
              // Streak broken - reset to 1
              newStreak = 1;
            }
            
            completedDates.push(today);
          }
          
          // Update progress
          let newProgress = habit.progress || 0;
          if (habit.goal && habit.goal > 1) {
            newProgress = Math.min(1, (newProgress || 0) + (1 / habit.goal));
          } else {
            newProgress = 1;
          }
          
          const updatedHabit = {
            ...habit,
            completedToday: true,
            completedDates,
            streak: newStreak,
            progress: newProgress,
          };
          
          // Set the habit to celebrate
          setCelebratingHabitId(id);
          
          return updatedHabit;
        }
        return habit;
      })
    );
  };

  const deleteHabit = async (id: string) => {
    setHabits(prevHabits => prevHabits.filter(habit => habit.id !== id));
  };

  const editHabit = async (updatedHabit: Habit) => {
    setHabits(prevHabits => 
      prevHabits.map(habit => 
        habit.id === updatedHabit.id ? updatedHabit : habit
      )
    );
  };

  const resetTodayHabits = async () => {
    setHabits(prevHabits => 
      prevHabits.map(habit => ({
        ...habit,
        completedToday: false,
        progress: 0,
      }))
    );
  };

  return (
    <HabitsContext.Provider
      value={{
        habits,
        loading,
        addHabit,
        completeHabit,
        deleteHabit,
        editHabit,
        resetTodayHabits,
        celebratingHabitId,
        setCelebratingHabitId,
        streakMilestone,
        setStreakMilestone,
      }}
    >
      {children}
    </HabitsContext.Provider>
  );
};

export const useHabits = () => {
  const context = useContext(HabitsContext);
  if (context === undefined) {
    throw new Error('useHabits must be used within a HabitsProvider');
  }
  return context;
}; 